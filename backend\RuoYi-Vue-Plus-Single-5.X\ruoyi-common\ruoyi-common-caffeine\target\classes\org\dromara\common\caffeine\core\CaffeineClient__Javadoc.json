{"doc": " Caffeine缓存客户端\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "get", "paramTypes": ["java.lang.String"], "doc": " 获取缓存值\n\n @param key 缓存键\n @return 缓存值\n"}, {"name": "get", "paramTypes": ["java.lang.String", "java.util.function.Function"], "doc": " 获取缓存值，如果不存在则使用loader加载\n\n @param key    缓存键\n @param loader 加载器\n @return 缓存值\n"}, {"name": "put", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 设置缓存值\n\n @param key   缓存键\n @param value 缓存值\n"}, {"name": "put", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": " 设置带过期时间的缓存值\n\n @param key        缓存键\n @param value      缓存值\n @param ttlSeconds 过期时间（秒）\n"}, {"name": "putAll", "paramTypes": ["java.util.Map"], "doc": " 批量设置缓存\n\n @param map 缓存键值对\n"}, {"name": "evict", "paramTypes": ["java.lang.String"], "doc": " 删除缓存\n\n @param key 缓存键\n"}, {"name": "evictAll", "paramTypes": ["java.util.Collection"], "doc": " 批量删除缓存\n\n @param keys 缓存键集合\n"}, {"name": "clear", "paramTypes": [], "doc": " 清空所有缓存\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": " 检查缓存是否存在\n\n @param key 缓存键\n @return 是否存在\n"}, {"name": "size", "paramTypes": [], "doc": " 获取缓存大小\n\n @return 缓存大小\n"}, {"name": "keys", "paramTypes": [], "doc": " 获取所有缓存键\n\n @return 缓存键集合\n"}, {"name": "stats", "paramTypes": [], "doc": " 获取缓存统计信息\n\n @return 统计信息\n"}, {"name": "cleanUp", "paramTypes": [], "doc": " 执行缓存清理\n"}, {"name": "getTtl", "paramTypes": ["java.lang.String"], "doc": " 获取缓存剩余过期时间\n\n @param key 缓存键\n @return 剩余秒数，-1表示永不过期，0表示已过期或不存在\n"}, {"name": "expire", "paramTypes": ["java.lang.String", "long"], "doc": " 设置缓存过期时间\n\n @param key        缓存键\n @param ttlSeconds 过期时间（秒）\n @return 是否设置成功\n"}, {"name": "getNativeCache", "paramTypes": [], "doc": " 获取原始缓存对象\n\n @return 原始缓存\n"}], "constructors": []}