{"doc": " stream 流工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "filter", "paramTypes": ["java.util.Collection", "java.util.function.Predicate"], "doc": " 将collection过滤\n\n @param collection 需要转化的集合\n @param function   过滤方法\n @return 过滤后的list\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.util.Collection", "java.util.function.Predicate"], "doc": " 找到流中满足条件的第一个元素\n\n @param collection 需要查询的集合\n @param function   过滤方法\n @return 找到符合条件的第一个元素，没有则返回null\n"}, {"name": "findAny", "paramTypes": ["java.util.Collection", "java.util.function.Predicate"], "doc": " 找到流中任意一个满足条件的元素\n\n @param collection 需要查询的集合\n @param function   过滤方法\n @return 找到符合条件的任意一个元素，没有则返回null\n"}, {"name": "join", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": " 将collection拼接\n\n @param collection 需要转化的集合\n @param function   拼接方法\n @return 拼接后的list\n"}, {"name": "join", "paramTypes": ["java.util.Collection", "java.util.function.Function", "java.lang.CharSequence"], "doc": " 将collection拼接\n\n @param collection 需要转化的集合\n @param function   拼接方法\n @param delimiter  拼接符\n @return 拼接后的list\n"}, {"name": "sorted", "paramTypes": ["java.util.Collection", "java.util.Comparator"], "doc": " 将collection排序\n\n @param collection 需要转化的集合\n @param comparing  排序方法\n @return 排序后的list\n"}, {"name": "toIdentityMap", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": " 将collection转化为类型不变的map<br>\n <B>{@code Collection<V>  ---->  Map<K,V>}</B>\n\n @param collection 需要转化的集合\n @param key        V类型转化为K类型的lambda方法\n @param <V>        collection中的泛型\n @param <K>        map中的key类型\n @return 转化后的map\n"}, {"name": "toMap", "paramTypes": ["java.util.Collection", "java.util.function.Function", "java.util.function.Function"], "doc": " 将Collection转化为map(value类型与collection的泛型不同)<br>\n <B>{@code Collection<E> -----> Map<K,V>  }</B>\n\n @param collection 需要转化的集合\n @param key        E类型转化为K类型的lambda方法\n @param value      E类型转化为V类型的lambda方法\n @param <E>        collection中的泛型\n @param <K>        map中的key类型\n @param <V>        map中的value类型\n @return 转化后的map\n"}, {"name": "groupByKey", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": " 将collection按照规则(比如有相同的班级id)分类成map<br>\n <B>{@code Collection<E> -------> Map<K,List<E>> } </B>\n\n @param collection 需要分类的集合\n @param key        分类的规则\n @param <E>        collection中的泛型\n @param <K>        map中的key类型\n @return 分类后的map\n"}, {"name": "groupBy2Key", "paramTypes": ["java.util.Collection", "java.util.function.Function", "java.util.function.Function"], "doc": " 将collection按照两个规则(比如有相同的年级id,班级id)分类成双层map<br>\n <B>{@code Collection<E>  --->  Map<T,Map<U,List<E>>> } </B>\n\n @param collection 需要分类的集合\n @param key1       第一个分类的规则\n @param key2       第二个分类的规则\n @param <E>        集合元素类型\n @param <K>        第一个map中的key类型\n @param <U>        第二个map中的key类型\n @return 分类后的map\n"}, {"name": "group2Map", "paramTypes": ["java.util.Collection", "java.util.function.Function", "java.util.function.Function"], "doc": " 将collection按照两个规则(比如有相同的年级id,班级id)分类成双层map<br>\n <B>{@code Collection<E>  --->  Map<T,Map<U,E>> } </B>\n\n @param collection 需要分类的集合\n @param key1       第一个分类的规则\n @param key2       第二个分类的规则\n @param <T>        第一个map中的key类型\n @param <U>        第二个map中的key类型\n @param <E>        collection中的泛型\n @return 分类后的map\n"}, {"name": "toList", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": " 将collection转化为List集合，但是两者的泛型不同<br>\n <B>{@code Collection<E>  ------>  List<T> } </B>\n\n @param collection 需要转化的集合\n @param function   collection中的泛型转化为list泛型的lambda表达式\n @param <E>        collection中的泛型\n @param <T>        List中的泛型\n @return 转化后的list\n"}, {"name": "toSet", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": " 将collection转化为Set集合，但是两者的泛型不同<br>\n <B>{@code Collection<E>  ------>  Set<T> } </B>\n\n @param collection 需要转化的集合\n @param function   collection中的泛型转化为set泛型的lambda表达式\n @param <E>        collection中的泛型\n @param <T>        Set中的泛型\n @return 转化后的Set\n"}, {"name": "merge", "paramTypes": ["java.util.Map", "java.util.Map", "java.util.function.BiFunction"], "doc": " 合并两个相同key类型的map\n\n @param map1  第一个需要合并的 map\n @param map2  第二个需要合并的 map\n @param merge 合并的lambda，将key  value1 value2合并成最终的类型,注意value可能为空的情况\n @param <K>   map中的key类型\n @param <X>   第一个 map的value类型\n @param <Y>   第二个 map的value类型\n @param <V>   最终map的value类型\n @return 合并后的map\n"}], "constructors": []}