{"doc": " 注册校验方法\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "register", "paramTypes": ["org.dromara.common.core.domain.model.RegisterBody"], "doc": " 注册\n"}, {"name": "validate<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 校验验证码\n\n @param username 用户名\n @param code     验证码\n @param uuid     唯一标识\n"}, {"name": "recordLogininfor", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 记录登录信息\n\n @param username 用户名\n @param status   状态\n @param message  消息内容\n @return\n"}], "constructors": []}