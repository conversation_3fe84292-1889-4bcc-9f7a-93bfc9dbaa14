{"doc": " Caffeine缓存配置属性\n\n <AUTHOR>\n", "fields": [{"name": "enabled", "doc": " 是否开启\n"}, {"name": "defaultCacheName", "doc": " 默认缓存名称\n"}, {"name": "maximumSize", "doc": " 缓存最大条目数\n"}, {"name": "expireAfterWrite", "doc": " 写入后过期时间（秒）\n"}, {"name": "expireAfterAccess", "doc": " 访问后过期时间（秒）\n"}, {"name": "refreshAfterWrite", "doc": " 刷新时间（秒）\n"}, {"name": "initialCapacity", "doc": " 初始容量\n"}, {"name": "recordStats", "doc": " 是否启用统计\n"}, {"name": "weakKeys", "doc": " 是否启用软引用\n"}, {"name": "weakValues", "doc": " 是否启用弱引用值\n"}, {"name": "softValues", "doc": " 是否启用软引用值\n"}, {"name": "debugEnabled", "doc": " 是否启用调试日志\n"}], "enumConstants": [], "methods": [], "constructors": []}