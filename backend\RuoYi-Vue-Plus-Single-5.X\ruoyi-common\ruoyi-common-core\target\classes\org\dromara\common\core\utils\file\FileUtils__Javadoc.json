{"doc": " 文件处理工具类\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "setAttachmentResponseHeader", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": " 下载文件名重新编码\n\n @param response     响应对象\n @param realFileName 真实文件名\n"}, {"name": "percentEncode", "paramTypes": ["java.lang.String"], "doc": " 百分号编码工具方法\n\n @param s 需要百分号编码的字符串\n @return 百分号编码后的字符串\n"}, {"name": "isValidFileExtention", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String[]"], "doc": " 检查文件扩展名是否符合要求\n\n @param file\n @return\n"}, {"name": "getSecureFilePathForUpload", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取安全的文件路径\n\n @param originalFilename 原始文件名\n @param secureFilePath   安全路径\n @return 安全文件路径\n"}], "constructors": []}