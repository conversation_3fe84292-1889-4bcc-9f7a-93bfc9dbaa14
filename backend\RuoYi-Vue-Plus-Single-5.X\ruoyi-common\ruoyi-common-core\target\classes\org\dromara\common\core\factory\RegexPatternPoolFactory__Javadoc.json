{"doc": " 正则表达式模式池工厂\n <p>初始化的时候将正则表达式加入缓存池当中</p>\n <p>提高正则表达式的性能，避免重复编译相同的正则表达式</p>\n\n <AUTHOR>\n", "fields": [{"name": "DICTIONARY_TYPE", "doc": " 字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）\n"}, {"name": "ID_CARD_LAST_6", "doc": " 身份证号码（后6位）\n"}, {"name": "QQ_NUMBER", "doc": " QQ号码\n"}, {"name": "POSTAL_CODE", "doc": " 邮政编码\n"}, {"name": "ACCOUNT", "doc": " 注册账号\n"}, {"name": "PASSWORD", "doc": " 密码：包含至少8个字符，包括大写字母、小写字母、数字和特殊字符\n"}, {"name": "STATUS", "doc": " 通用状态（0表示正常，1表示停用）\n"}], "enumConstants": [], "methods": [], "constructors": []}