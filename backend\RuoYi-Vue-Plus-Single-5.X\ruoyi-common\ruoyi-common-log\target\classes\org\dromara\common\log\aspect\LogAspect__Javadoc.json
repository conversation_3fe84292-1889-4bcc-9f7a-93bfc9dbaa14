{"doc": " 操作日志记录处理\n\n <AUTHOR>\n", "fields": [{"name": "EXCLUDE_PROPERTIES", "doc": " 排除敏感属性字段\n"}, {"name": "KEY_CACHE", "doc": " 计时 key\n"}], "enumConstants": [], "methods": [{"name": "doBefore", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.annotation.Log"], "doc": " 处理请求前执行\n"}, {"name": "doAfterReturning", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.annotation.Log", "java.lang.Object"], "doc": " 处理完请求后执行\n\n @param joinPoint 切点\n"}, {"name": "doAfterThrowing", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.annotation.Log", "java.lang.Exception"], "doc": " 拦截异常操作\n\n @param joinPoint 切点\n @param e         异常\n"}, {"name": "getControllerMethodDescription", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.annotation.Log", "org.dromara.common.log.event.OperLogEvent", "java.lang.Object"], "doc": " 获取注解中对方法的描述信息 用于Controller层注解\n\n @param log     日志\n @param operLog 操作日志\n @throws Exception\n"}, {"name": "setRequestValue", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.log.event.OperLogEvent", "java.lang.String[]"], "doc": " 获取请求的参数，放到log中\n\n @param operLog 操作日志\n @throws Exception 异常\n"}, {"name": "argsArrayToString", "paramTypes": ["java.lang.Object[]", "java.lang.String[]"], "doc": " 参数拼装\n"}, {"name": "isFilterObject", "paramTypes": ["java.lang.Object"], "doc": " 判断是否需要过滤的对象。\n\n @param o 对象信息。\n @return 如果是需要过滤的对象，则返回true；否则返回false。\n"}], "constructors": []}