{"doc": " 日期格式与时间格式枚举\n", "fields": [{"name": "timeFormat", "doc": " 时间格式\n"}], "enumConstants": [{"name": "YY", "doc": " 例如：2023年表示为\"23\"\n"}, {"name": "YYYY", "doc": " 例如：2023年表示为\"2023\"\n"}, {"name": "YYYY_MM", "doc": " 例例如，2023年7月可以表示为 \"2023-07\"\n"}, {"name": "YYYY_MM_DD", "doc": " 例如，日期 \"2023年7月22日\" 可以表示为 \"2023-07-22\"\n"}, {"name": "YYYY_MM_DD_HH_MM", "doc": " 例如，当前时间如果是 \"2023年7月22日下午3点30分\"，则可以表示为 \"2023-07-22 15:30\"\n"}, {"name": "YYYY_MM_DD_HH_MM_SS", "doc": " 例如，当前时间如果是 \"2023年7月22日下午3点30分45秒\"，则可以表示为 \"2023-07-22 15:30:45\"\n"}, {"name": "HH_MM_SS", "doc": " 例如：下午3点30分45秒，表示为 \"15:30:45\"\n"}, {"name": "YYYY_MM_SLASH", "doc": " 例例如，2023年7月可以表示为 \"2023/07\"\n"}, {"name": "YYYY_MM_DD_SLASH", "doc": " 例如，日期 \"2023年7月22日\" 可以表示为 \"2023/07/22\"\n"}, {"name": "YYYY_MM_DD_HH_MM_SLASH", "doc": " 例如，当前时间如果是 \"2023年7月22日下午3点30分45秒\"，则可以表示为 \"2023/07/22 15:30:45\"\n"}, {"name": "YYYY_MM_DD_HH_MM_SS_SLASH", "doc": " 例如，当前时间如果是 \"2023年7月22日下午3点30分45秒\"，则可以表示为 \"2023/07/22 15:30:45\"\n"}, {"name": "YYYY_MM_DOT", "doc": " 例例如，2023年7月可以表示为 \"2023.07\"\n"}, {"name": "YYYY_MM_DD_DOT", "doc": " 例如，日期 \"2023年7月22日\" 可以表示为 \"2023.07.22\"\n"}, {"name": "YYYY_MM_DD_HH_MM_DOT", "doc": " 例如，当前时间如果是 \"2023年7月22日下午3点30分\"，则可以表示为 \"2023.07.22 15:30\"\n"}, {"name": "YYYY_MM_DD_HH_MM_SS_DOT", "doc": " 例如，当前时间如果是 \"2023年7月22日下午3点30分45秒\"，则可以表示为 \"2023.07.22 15:30:45\"\n"}, {"name": "YYYYMM", "doc": " 例如，2023年7月可以表示为 \"202307\"\n"}, {"name": "YYYYMMDD", "doc": " 例如，2023年7月22日可以表示为 \"20230722\"\n"}, {"name": "YYYYMMDDHH", "doc": " 例如，2023年7月22日下午3点可以表示为 \"2023072215\"\n"}, {"name": "YYYYMMDDHHMM", "doc": " 例如，2023年7月22日下午3点30分可以表示为 \"202307221530\"\n"}, {"name": "YYYYMMDDHHMMSS", "doc": " 例如，2023年7月22日下午3点30分45秒可以表示为 \"20230722153045\"\n"}], "methods": [], "constructors": []}