{"doc": " JSON 工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "toJsonString", "paramTypes": ["java.lang.Object"], "doc": " 将对象转换为JSON格式的字符串\n\n @param object 要转换的对象\n @return JSON格式的字符串，如果对象为null，则返回null\n @throws RuntimeException 如果转换过程中发生JSON处理异常，则抛出运行时异常\n"}, {"name": "parseObject", "paramTypes": ["java.lang.String", "java.lang.Class"], "doc": " 将JSON格式的字符串转换为指定类型的对象\n\n @param text  JSON格式的字符串\n @param clazz 要转换的目标对象类型\n @param <T>   目标对象的泛型类型\n @return 转换后的对象，如果字符串为空则返回null\n @throws RuntimeException 如果转换过程中发生IO异常，则抛出运行时异常\n"}, {"name": "parseObject", "paramTypes": ["byte[]", "java.lang.Class"], "doc": " 将字节数组转换为指定类型的对象\n\n @param bytes 字节数组\n @param clazz 要转换的目标对象类型\n @param <T>   目标对象的泛型类型\n @return 转换后的对象，如果字节数组为空则返回null\n @throws RuntimeException 如果转换过程中发生IO异常，则抛出运行时异常\n"}, {"name": "parseObject", "paramTypes": ["java.lang.String", "com.fasterxml.jackson.core.type.TypeReference"], "doc": " 将JSON格式的字符串转换为指定类型的对象，支持复杂类型\n\n @param text          JSON格式的字符串\n @param typeReference 指定类型的TypeReference对象\n @param <T>           目标对象的泛型类型\n @return 转换后的对象，如果字符串为空则返回null\n @throws RuntimeException 如果转换过程中发生IO异常，则抛出运行时异常\n"}, {"name": "parseMap", "paramTypes": ["java.lang.String"], "doc": " 将JSON格式的字符串转换为Dict对象\n\n @param text JSON格式的字符串\n @return 转换后的Dict对象，如果字符串为空或者不是JSON格式则返回null\n @throws RuntimeException 如果转换过程中发生IO异常，则抛出运行时异常\n"}, {"name": "parseArrayMap", "paramTypes": ["java.lang.String"], "doc": " 将JSON格式的字符串转换为Dict对象的列表\n\n @param text JSON格式的字符串\n @return 转换后的Dict对象的列表，如果字符串为空则返回null\n @throws RuntimeException 如果转换过程中发生IO异常，则抛出运行时异常\n"}, {"name": "parseArray", "paramTypes": ["java.lang.String", "java.lang.Class"], "doc": " 将JSON格式的字符串转换为指定类型对象的列表\n\n @param text  JSON格式的字符串\n @param clazz 要转换的目标对象类型\n @param <T>   目标对象的泛型类型\n @return 转换后的对象的列表，如果字符串为空则返回空列表\n @throws RuntimeException 如果转换过程中发生IO异常，则抛出运行时异常\n"}], "constructors": []}