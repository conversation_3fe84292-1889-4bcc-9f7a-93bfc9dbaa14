{"doc": " 数据权限助手\n\n <AUTHOR>\n @version 3.5.0\n", "fields": [], "enumConstants": [], "methods": [{"name": "getPermission", "paramTypes": [], "doc": " 获取当前执行mapper权限注解\n\n @return 返回当前执行mapper权限注解\n"}, {"name": "setPermission", "paramTypes": ["org.dromara.common.mybatis.annotation.DataPermission"], "doc": " 设置当前执行mapper权限注解\n\n @param dataPermission   数据权限注解\n"}, {"name": "removePermission", "paramTypes": [], "doc": " 删除当前执行mapper权限注解\n"}, {"name": "getVariable", "paramTypes": ["java.lang.String"], "doc": " 从上下文中获取指定键的变量值，并将其转换为指定的类型\n\n @param key 变量的键\n @param <T> 变量值的类型\n @return 指定键的变量值，如果不存在则返回 null\n"}, {"name": "setVariable", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 向上下文中设置指定键的变量值\n\n @param key   要设置的变量的键\n @param value 要设置的变量值\n"}, {"name": "getContext", "paramTypes": [], "doc": " 获取数据权限上下文\n\n @return 存储在SaStorage中的Map对象，用于存储数据权限相关的上下文信息\n @throws NullPointerException 如果数据权限上下文类型异常，则抛出NullPointerException\n"}, {"name": "enableIgnore", "paramTypes": [], "doc": " 开启忽略数据权限(开启后需手动调用 {@link #disableIgnore()} 关闭)\n"}, {"name": "disableIgnore", "paramTypes": [], "doc": " 关闭忽略数据权限\n"}, {"name": "ignore", "paramTypes": ["java.lang.Runnable"], "doc": " 在忽略数据权限中执行\n\n @param handle 处理执行方法\n"}, {"name": "ignore", "paramTypes": ["java.util.function.Supplier"], "doc": " 在忽略数据权限中执行\n\n @param handle 处理执行方法\n"}], "constructors": []}