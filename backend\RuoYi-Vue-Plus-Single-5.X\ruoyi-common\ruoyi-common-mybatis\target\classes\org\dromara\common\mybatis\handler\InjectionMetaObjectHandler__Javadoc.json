{"doc": " MP注入处理器\n\n <AUTHOR>\n @date 2021/4/25\n", "fields": [], "enumConstants": [], "methods": [{"name": "insertFill", "paramTypes": ["org.apache.ibatis.reflection.MetaObject"], "doc": " 插入填充方法，用于在插入数据时自动填充实体对象中的创建时间、更新时间、创建人、更新人等信息\n\n @param metaObject 元对象，用于获取原始对象并进行填充\n"}, {"name": "updateFill", "paramTypes": ["org.apache.ibatis.reflection.MetaObject"], "doc": " 更新填充方法，用于在更新数据时自动填充实体对象中的更新时间和更新人信息\n\n @param metaObject 元对象，用于获取原始对象并进行填充\n"}, {"name": "getLoginUser", "paramTypes": [], "doc": " 获取当前登录用户信息\n\n @return 当前登录用户的信息，如果用户未登录则返回 null\n"}], "constructors": []}