{"doc": " 数据权限类型枚举\n <p>\n 支持使用 SpEL 模板表达式定义 SQL 查询条件\n 内置数据：\n - {@code user}: 当前登录用户信息，参考 {@link LoginUser}\n 内置服务：\n - {@code sdss}: 系统数据权限服务，参考 ISysDataScopeService\n 如需扩展数据，可以通过 {@link DataPermissionHelper} 进行操作\n 如需扩展服务，可以通过 ISysDataScopeService 自行编写\n </p>\n\n <AUTHOR>\n @version 3.5.0\n", "fields": [{"name": "sqlTemplate", "doc": " SpEL 模板表达式，用于构建 SQL 查询条件\n"}, {"name": "elseSql", "doc": " 如果不满足 {@code sqlTemplate} 的条件，则使用此默认 SQL 表达式\n"}], "enumConstants": [{"name": "ALL", "doc": " 全部数据权限\n"}, {"name": "CUSTOM", "doc": " 自定数据权限\n"}, {"name": "DEPT", "doc": " 部门数据权限\n"}, {"name": "DEPT_AND_CHILD", "doc": " 部门及以下数据权限\n"}, {"name": "SELF", "doc": " 仅本人数据权限\n"}, {"name": "DEPT_AND_CHILD_OR_SELF", "doc": " 部门及以下或本人数据权限\n"}], "methods": [{"name": "findCode", "paramTypes": ["java.lang.String"], "doc": " 根据枚举代码查找对应的枚举值\n\n @param code 枚举代码\n @return 对应的枚举值，如果未找到则返回 null\n"}], "constructors": []}