{"doc": " 支付宝支付工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPayPage", "paramTypes": ["com.alipay.api.AlipayClient", "org.dromara.common.pay.properties.AlipayProperties", "java.lang.String", "java.lang.String", "java.math.BigDecimal", "java.lang.String"], "doc": " 创建支付宝支付页面\n\n @param alipayClient 支付宝客户端\n @param alipayProperties 支付宝配置\n @param orderNo 订单号\n @param subject 商品标题\n @param totalAmount 支付金额\n @param body 商品描述\n @return 支付页面HTML\n"}, {"name": "validateCreatePayPageParams", "paramTypes": ["com.alipay.api.AlipayClient", "org.dromara.common.pay.properties.AlipayProperties", "java.lang.String", "java.lang.String", "java.math.BigDecimal"], "doc": " 验证创建支付页面的参数\n\n @param alipayClient 支付宝客户端\n @param alipayProperties 支付宝配置\n @param orderNo 订单号\n @param subject 商品标题\n @param totalAmount 支付金额\n"}, {"name": "validateAlipayConfig", "paramTypes": ["org.dromara.common.pay.properties.AlipayProperties"], "doc": " 验证支付宝关键配置\n\n @param alipayProperties 支付宝配置\n"}, {"name": "isValidUrl", "paramTypes": ["java.lang.String"], "doc": " 验证URL格式是否正确\n\n @param url URL地址\n @return true-格式正确 false-格式错误\n"}, {"name": "queryTradeStatus", "paramTypes": ["com.alipay.api.AlipayClient", "java.lang.String"], "doc": " 查询支付宝订单状态\n\n @param alipayClient 支付宝客户端\n @param orderNo 订单号\n @return 查询响应\n"}, {"name": "verifyNotify", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 验证支付宝异步通知签名\n\n @param params 通知参数\n @param alipayPublicKey 支付宝公钥\n @param charset 字符编码\n @param signType 签名类型\n @return true-验证成功 false-验证失败\n"}, {"name": "verifyReturn", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 验证支付宝同步回调签名\n\n @param params 回调参数\n @param alipayPublicKey 支付宝公钥\n @param charset 字符编码\n @param signType 签名类型\n @return true-验证成功 false-验证失败\n"}, {"name": "convertTradeStatus", "paramTypes": ["java.lang.String"], "doc": " 根据支付宝交易状态转换为系统支付状态\n\n @param tradeStatus 支付宝交易状态\n @return 系统支付状态\n"}, {"name": "formatAmount", "paramTypes": ["java.math.BigDecimal"], "doc": " 格式化金额为支付宝格式（保留两位小数）\n\n @param amount 金额\n @return 格式化后的金额字符串\n"}, {"name": "validateAmount", "paramTypes": ["java.math.BigDecimal"], "doc": " 验证支付金额范围\n\n @param amount 支付金额（元）\n @throws PaymentException 金额超出范围时抛出异常\n"}], "constructors": []}