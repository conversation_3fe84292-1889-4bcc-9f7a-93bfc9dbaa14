{"doc": " 支付核心服务类\n 提供统一的支付服务接口，支持多种支付方式\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPayment", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.lang.String", "java.lang.String", "java.math.BigDecimal", "java.lang.String"], "doc": " 创建支付订单\n\n @param paymentMethod 支付方式\n @param orderNo 订单号\n @param subject 商品标题\n @param totalAmount 支付金额（元）\n @param body 商品描述\n @return 支付结果（支付宝返回HTML表单，微信返回支付参数等）\n"}, {"name": "queryPaymentStatus", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.lang.String"], "doc": " 查询支付订单状态\n\n @param paymentMethod 支付方式\n @param orderNo 订单号\n @return 支付状态\n"}, {"name": "verifyPaymentNotify", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.util.Map"], "doc": " 验证支付异步通知\n\n @param paymentMethod 支付方式\n @param params 通知参数\n @return 通知结果\n"}, {"name": "verifyPaymentCallback", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.util.Map"], "doc": " 验证支付同步回调\n\n @param paymentMethod 支付方式\n @param params 回调参数\n @return 回调结果\n"}, {"name": "createAlipayPayment", "paramTypes": ["java.lang.String", "java.lang.String", "java.math.BigDecimal", "java.lang.String"], "doc": " 创建支付宝支付\n"}, {"name": "queryAlipayStatus", "paramTypes": ["java.lang.String"], "doc": " 查询支付宝支付状态\n"}, {"name": "verifyAlipayNotify", "paramTypes": ["java.util.Map"], "doc": " 验证支付宝异步通知\n"}, {"name": "verifyAlipayCallback", "paramTypes": ["java.util.Map"], "doc": " 验证支付宝同步回调\n"}, {"name": "validatePaymentParams", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.lang.String", "java.lang.String", "java.math.BigDecimal"], "doc": " 验证支付参数\n"}], "constructors": []}