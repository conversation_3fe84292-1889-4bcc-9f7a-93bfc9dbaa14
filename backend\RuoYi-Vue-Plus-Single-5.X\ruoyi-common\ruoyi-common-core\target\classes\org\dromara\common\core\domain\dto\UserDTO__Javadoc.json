{"doc": " 用户\n\n <AUTHOR>\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "deptId", "doc": " 部门ID\n"}, {"name": "userName", "doc": " 用户账号\n"}, {"name": "nick<PERSON><PERSON>", "doc": " 用户昵称\n"}, {"name": "userType", "doc": " 用户类型（sys_user系统用户）\n"}, {"name": "email", "doc": " 用户邮箱\n"}, {"name": "phonenumber", "doc": " 手机号码\n"}, {"name": "sex", "doc": " 用户性别（0男 1女 2未知）\n"}, {"name": "status", "doc": " 帐号状态（0正常 1停用）\n"}, {"name": "createTime", "doc": " 创建时间\n"}], "enumConstants": [], "methods": [], "constructors": []}