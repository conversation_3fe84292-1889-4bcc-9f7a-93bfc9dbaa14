{"doc": " MongoDB工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "save", "paramTypes": ["java.lang.Object"], "doc": " 保存文档\n"}, {"name": "saveAll", "paramTypes": ["java.util.Collection"], "doc": " 批量保存文档\n"}, {"name": "findById", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": " 根据ID查询文档\n"}, {"name": "findAll", "paramTypes": ["java.lang.Class"], "doc": " 查询所有文档\n"}, {"name": "find", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": " 根据条件查询文档\n"}, {"name": "findOne", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": " 根据条件查询单个文档\n"}, {"name": "findByField", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Class"], "doc": " 根据字段查询文档\n"}, {"name": "findOneByField", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Class"], "doc": " 根据字段查询单个文档\n"}, {"name": "findByPage", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "int", "int", "java.lang.Class"], "doc": " 分页查询\n"}, {"name": "findByPage", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "int", "int", "org.springframework.data.domain.Sort", "java.lang.Class"], "doc": " 分页查询带排序\n"}, {"name": "count", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": " 计数\n"}, {"name": "countAll", "paramTypes": ["java.lang.Class"], "doc": " 计数所有\n"}, {"name": "update", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "org.springframework.data.mongodb.core.query.Update", "java.lang.Class"], "doc": " 更新文档\n"}, {"name": "updateMulti", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "org.springframework.data.mongodb.core.query.Update", "java.lang.Class"], "doc": " 批量更新文档\n"}, {"name": "updateById", "paramTypes": ["java.lang.Object", "org.springframework.data.mongodb.core.query.Update", "java.lang.Class"], "doc": " 根据ID更新文档\n"}, {"name": "remove", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": " 删除文档\n"}, {"name": "removeById", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": " 根据ID删除文档\n"}, {"name": "removeAll", "paramTypes": ["java.lang.Class"], "doc": " 删除所有文档\n"}, {"name": "exists", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": " 检查文档是否存在\n"}, {"name": "existsById", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": " 根据ID检查文档是否存在\n"}, {"name": "createQuery", "paramTypes": [], "doc": " 创建查询条件\n"}, {"name": "createUpdate", "paramTypes": [], "doc": " 创建更新条件\n"}, {"name": "createSort", "paramTypes": ["org.springframework.data.domain.Sort.Direction", "java.lang.String[]"], "doc": " 创建排序条件\n"}, {"name": "createSortAsc", "paramTypes": ["java.lang.String[]"], "doc": " 创建升序排序\n"}, {"name": "createSortDesc", "paramTypes": ["java.lang.String[]"], "doc": " 创建降序排序\n"}, {"name": "buildLikeCriteria", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 构建模糊查询条件\n"}, {"name": "buildRangeCriteria", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Object"], "doc": " 构建范围查询条件\n"}, {"name": "buildInCriteria", "paramTypes": ["java.lang.String", "java.util.Collection"], "doc": " 构建IN查询条件\n"}], "constructors": []}