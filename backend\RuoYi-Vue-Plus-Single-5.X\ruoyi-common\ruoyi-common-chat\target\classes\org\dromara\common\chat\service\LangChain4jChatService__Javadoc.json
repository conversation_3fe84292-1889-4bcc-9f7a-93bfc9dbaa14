{"doc": " 基于LangChain4j的聊天服务\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "general<PERSON><PERSON>", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 通用聊天\n"}, {"name": "conductInterview", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 面试对话\n"}, {"name": "generateInterviewQuestions", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "int"], "doc": " 生成面试问题\n"}, {"name": "evaluateInterviewAnswer", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 评估面试回答\n"}, {"name": "analyzeResume", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 分析简历\n"}, {"name": "matchResumeWithJob", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 简历与职位匹配\n"}, {"name": "extractResumeKeyInfo", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 提取简历关键信息\n"}, {"name": "optimizeResumeForPosition", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 优化简历\n"}, {"name": "assessSkill", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 技能评估\n"}, {"name": "generateSkillTest", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "int"], "doc": " 生成技能测试\n"}, {"name": "evaluateSkillAnswer", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 评估技能答案\n"}, {"name": "generateSkillReport", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 生成技能报告\n"}, {"name": "createLearningPath", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 创建学习路径\n"}, {"name": "provideCareerAdvice", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 职业建议\n"}, {"name": "createCareerPlan", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 制定职业规划\n"}, {"name": "analyzeIndustryTrends", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 分析行业趋势\n"}, {"name": "guideCareerTransition", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 职业转换指导\n"}, {"name": "provideSalaryNegotiationAdvice", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 薪资谈判建议\n"}, {"name": "conductMockInterview", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 模拟面试\n"}, {"name": "designInterviewFlow", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 设计面试流程\n"}, {"name": "conductStressInterview", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 压力面试\n"}, {"name": "provideMockInterviewFeedback", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 面试反馈\n"}, {"name": "prepareQuestionType", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 问题类型准备\n"}, {"name": "provideLearningGuidance", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": " 学习指导\n"}, {"name": "createLearningPlan", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 制定学习计划\n"}, {"name": "recommendLearningResources", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 推荐学习资源\n"}, {"name": "analyzeLearningProgress", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 分析学习进度\n"}, {"name": "provideLearningMethods", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 提供学习方法\n"}, {"name": "buildKnowledgeMap", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": " 构建知识图谱\n"}, {"name": "clearSessionCache", "paramTypes": ["java.lang.String"], "doc": " 清理会话缓存\n"}], "constructors": []}