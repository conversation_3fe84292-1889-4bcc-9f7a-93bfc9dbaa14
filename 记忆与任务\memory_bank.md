# SmartInterview AI智能面试系统 - 项目记忆中枢 v1.0

## 项目概述
**项目名称**: SmartInterview AI智能面试系统  
**项目类型**: 多端智能面试平台（Web + 移动端）  
**核心目标**: 基于AI Agent技术，为用户提供全方位的面试准备和技能评估服务  
**创建时间**: 2025-07-16  
**最后更新**: 2025-07-16 15:38  

## 技术架构

### 后端架构
- **框架**: RuoYi-Vue-Plus 5.4.0 (Spring Boot 3.4.6)
- **Java版本**: JDK 17
- **数据库**: MySQL 8.0
- **缓存**: Redis (Redisson 3.45.1)
- **ORM**: MyBatis-Plus 3.5.12
- **认证**: Sa-Token 1.42.0
- **消息队列**: SnailJob 1.5.0
- **文档**: SpringDoc 2.8.8

### 前端架构
- **框架**: UniApp (Vue 3.4.21)
- **UI库**: Wot Design Uni 1.4.0
- **状态管理**: Pinia 2.0.36
- **样式**: TailwindCSS 4.1.7 + UnoCSS 0.58.9
- **构建工具**: Vite 5.2.8
- **TypeScript**: 5.7.2

### 项目结构
```
softwart-xunfei-code/
├── backend/RuoYi-Vue-Plus-Single-5.X/     # 后端服务
│   ├── ruoyi-admin/                       # 管理后台
│   ├── ruoyi-common/                      # 公共模块
│   ├── ruoyi-modules/                     # 业务模块
│   │   ├── ruoyi-app/                     # 移动端API
│   │   ├── ruoyi-chat/                    # 聊天模块
│   │   ├── ruoyi-system/                  # 系统管理
│   │   └── ruoyi-workflow/                # 工作流
│   └── ruoyi-extend/                      # 扩展模块
├── front/unibest-main/                    # 前端应用
│   ├── src/pages/                         # 页面模块
│   │   ├── aichat/                        # AI聊天
│   │   ├── interview/                     # 面试模块
│   │   ├── assessment/                    # 技能评估
│   │   ├── learning/                      # 学习模块
│   │   └── user/                          # 用户中心
│   ├── src/service/                       # 服务层
│   └── src/components/                    # 组件库
└── doc/                                   # 项目文档
    └── ai-agent-implementation-plan.md    # AI Agent实现方案
```

## 核心功能模块

### 1. AI Agent系统
- **面试官AI Agent**: 智能问题生成、动态追问、多轮对话管理
- **简历分析AI Agent**: 简历解析、技能匹配、优化建议
- **技能评估AI Agent**: 技术能力测试、编程评估、能力报告
- **职业顾问AI Agent**: 职业规划、行业分析、薪资预测
- **模拟面试AI Agent**: 真实面试模拟、多场景训练
- **反馈分析AI Agent**: 表现分析、多维评分、改进建议
- **学习指导AI Agent**: 学习计划、资源推荐、进度跟踪

### 2. 前端页面模块
- **AI聊天页面**: 智能对话交互界面
- **面试模块**: 面试流程管理和执行
- **技能评估**: 能力测试和评估报告
- **学习中心**: 个性化学习资源和计划
- **用户中心**: 个人信息和成长记录
- **成就系统**: 徽章管理和成就展示

### 3. 数据模型设计
- **AI Agent会话表**: 管理AI对话会话
- **AI对话记录表**: 存储对话历史
- **技能评估结果表**: 记录评估数据
- **面试记录表**: 面试过程和结果

## 开发规范

### 代码规范
- **后端**: 遵循阿里巴巴Java开发手册
- **前端**: 使用ESLint + Prettier + TypeScript严格模式
- **数据库**: 统一命名规范，使用下划线分隔
- **API**: RESTful设计，统一返回格式

### 技术约定
- **包管理**: 前端使用pnpm，后端使用Maven
- **版本控制**: Git工作流，feature分支开发
- **文档**: 接口文档使用SpringDoc自动生成
- **测试**: 单元测试覆盖率>80%

## 部署环境
- **开发环境**: Docker Compose本地部署
- **生产环境**: Kubernetes容器编排
- **数据库**: MySQL主从复制
- **缓存**: Redis集群
- **监控**: Spring Boot Admin + Prometheus

## 关键决策记录

### 技术选型决策
1. **选择RuoYi-Vue-Plus**: 成熟的企业级框架，内置权限管理、代码生成等功能
2. **选择UniApp**: 一套代码多端运行，支持H5、小程序、APP
3. **选择Sa-Token**: 轻量级权限认证框架，性能优于Spring Security
4. **选择TailwindCSS**: 原子化CSS，提高开发效率

### 架构决策
1. **单体架构**: 项目初期采用单体架构，便于快速开发和部署
2. **模块化设计**: 按业务功能划分模块，便于后期微服务拆分
3. **前后端分离**: API优先设计，支持多端访问

## 当前状态
- **项目阶段**: 开发阶段
- **完成度**: 基础框架搭建完成，AI Agent功能开发中
- **下一步**: 实现核心AI Agent功能模块

## 风险与挑战
1. **AI模型集成**: 需要集成第三方AI服务或自建模型
2. **性能优化**: 大量AI请求的并发处理
3. **数据安全**: 用户简历和面试数据的隐私保护
4. **成本控制**: AI服务调用成本管理

## 联系信息
- **开发团队**: TTB9
- **项目仓库**: 本地开发环境
- **文档位置**: ./doc/ai-agent-implementation-plan.md
