# <PERSON><PERSON>hain4j AI Agent 配置示例
langchain4j:
  # 是否启用LangChain4j
  enabled: true

  # OpenAI配置
  openai:
    api-key: ${OPENAI_API_KEY:sk-your-openai-api-key}
    base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
    model: ${OPENAI_MODEL:gpt-3.5-turbo}
    temperature: 0.7
    max-tokens: 2048
    timeout: 60

  # Ollama本地模型配置
  ollama:
    base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
    model: ${OLLAMA_MODEL:llama2}
    temperature: 0.7
    timeout: 60

  # 智谱AI配置
  zhipu-ai:
    api-key: ${ZHIPU_API_KEY:your-zhipu-api-key}
    model: ${ZHIPU_MODEL:glm-4}
    temperature: 0.7
    max-tokens: 2048

  # 阿里云通义千问配置
  dashscope:
    api-key: sk-7520b9ee487c408ab02f879768871023
    model: ${DASHSCOPE_MODEL:qwen-turbo}
    temperature: 0.7
    max-tokens: 2048

  # Agent配置
  agent:
    # 默认AI提供商 (openai, ollama, zhipu, dashscope)
    default-provider: ${AI_DEFAULT_PROVIDER:ollama}
    # 最大内存消息数
    max-memory-size: 10
    # 会话超时时间（秒）
    session-timeout: 3600
