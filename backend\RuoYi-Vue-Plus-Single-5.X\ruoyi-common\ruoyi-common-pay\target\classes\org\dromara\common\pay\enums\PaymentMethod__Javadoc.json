{"doc": " 支付方式枚举\n\n <AUTHOR>\n", "fields": [{"name": "code", "doc": " 支付方式代码\n"}, {"name": "name", "doc": " 支付方式名称\n"}], "enumConstants": [{"name": "ALIPAY", "doc": " 支付宝支付\n"}, {"name": "WECHAT", "doc": " 微信支付\n"}, {"name": "UNIONPAY", "doc": " 银联支付\n"}, {"name": "BALANCE", "doc": " 余额支付\n"}], "methods": [{"name": "fromCode", "paramTypes": ["java.lang.String"], "doc": " 根据代码获取支付方式\n\n @param code 支付方式代码\n @return PaymentMethod\n"}], "constructors": []}