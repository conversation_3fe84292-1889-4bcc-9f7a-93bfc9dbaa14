{"doc": " 分页查询实体类\n\n <AUTHOR>\n", "fields": [{"name": "pageSize", "doc": " 分页大小\n"}, {"name": "pageNum", "doc": " 当前页数\n"}, {"name": "orderByColumn", "doc": " 排序列\n"}, {"name": "isAsc", "doc": " 排序的方向desc或者asc\n"}, {"name": "DEFAULT_PAGE_NUM", "doc": " 当前记录起始索引 默认值\n"}, {"name": "DEFAULT_PAGE_SIZE", "doc": " 每页显示记录数 默认值 默认查全部\n"}], "enumConstants": [], "methods": [{"name": "build", "paramTypes": [], "doc": " 构建分页对象\n"}, {"name": "buildOrderItem", "paramTypes": [], "doc": " 构建排序\n\n 支持的用法如下:\n {isAsc:\"asc\",orderByColumn:\"id\"} order by id asc\n {isAsc:\"asc\",orderByColumn:\"id,createTime\"} order by id asc,create_time asc\n {isAsc:\"desc\",orderByColumn:\"id,createTime\"} order by id desc,create_time desc\n {isAsc:\"asc,desc\",orderByColumn:\"id,createTime\"} order by id asc,create_time desc\n"}], "constructors": []}