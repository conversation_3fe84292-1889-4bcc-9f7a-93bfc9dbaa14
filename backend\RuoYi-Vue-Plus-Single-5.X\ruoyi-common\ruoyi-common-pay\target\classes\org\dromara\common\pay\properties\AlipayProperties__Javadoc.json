{"doc": " 支付宝配置属性类\n\n <AUTHOR>\n", "fields": [{"name": "enabled", "doc": " 支付宝功能开关\n"}, {"name": "environment", "doc": " 环境类型 - sandbox(沙箱) 或 production(生产)\n"}, {"name": "appId", "doc": " 应用ID - 支付宝应用ID\n"}, {"name": "privateKey", "doc": " 商户私钥 - 应用私钥\n"}, {"name": "alipayPublicKey", "doc": " 支付宝公钥 - 支付宝公钥\n"}, {"name": "notifyUrl", "doc": " 服务器异步通知页面路径 - 支付成功后支付宝异步通知的地址\n"}, {"name": "returnUrl", "doc": " 页面跳转同步通知页面路径 - 支付成功后页面跳转的地址\n"}, {"name": "serverUrl", "doc": " 应用服务器地址 - 用于构建回调URL的基础地址\n"}, {"name": "signType", "doc": " 签名方式 - 默认RSA2\n"}, {"name": "charset", "doc": " 字符编码格式 - 默认UTF-8\n"}, {"name": "format", "doc": " 返回格式 - 默认JSON\n"}], "enumConstants": [], "methods": [{"name": "getGatewayUrl", "paramTypes": [], "doc": " 获取支付宝网关地址\n 根据环境类型自动选择对应的网关地址\n\n @return 网关地址\n"}, {"name": "isSandbox", "paramTypes": [], "doc": " 是否为沙箱环境\n\n @return true-沙箱环境 false-生产环境\n"}, {"name": "getNotifyUrl", "paramTypes": [], "doc": " 获取异步通知URL，如果未配置则使用默认值\n\n @return 异步通知URL\n"}, {"name": "getReturnUrl", "paramTypes": [], "doc": " 获取同步回调URL，如果未配置则使用默认值\n\n @return 同步回调URL\n"}, {"name": "buildDefaultNotifyUrl", "paramTypes": [], "doc": " 构建默认的异步通知URL\n\n @return 默认异步通知URL\n"}, {"name": "buildDefaultReturnUrl", "paramTypes": [], "doc": " 构建默认的同步回调URL\n\n @return 默认同步回调URL\n"}, {"name": "normalizeServerUrl", "paramTypes": [], "doc": " 规范化服务器URL，确保不以斜杠结尾\n\n @return 规范化后的服务器URL\n"}, {"name": "setNotifyUrl", "paramTypes": ["java.lang.String"], "doc": " 设置异步通知URL\n\n @param notifyUrl 异步通知URL\n"}, {"name": "setReturnUrl", "paramTypes": ["java.lang.String"], "doc": " 设置同步回调URL\n\n @param returnUrl 同步回调URL\n"}], "constructors": []}